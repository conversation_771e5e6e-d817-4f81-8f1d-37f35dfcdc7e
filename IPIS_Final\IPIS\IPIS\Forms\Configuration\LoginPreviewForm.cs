using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.IO;
using System.Windows.Forms;
using IPIS.Models;

namespace IPIS.Forms.Configuration
{
    public partial class LoginPreviewForm : Form
    {
        private readonly LoginConfiguration config;
        private Panel leftPanel;
        private Panel rightPanel;
        private Label welcomeLabel;
        private Label stationLabel;
        private Label subtitleLabel;
        private PictureBox logoBox;
        private Label titleLabel;
        private Panel usernamePanel;
        private Panel passwordPanel;
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Button btnLogin;

        public LoginPreviewForm(LoginConfiguration configuration)
        {
            config = configuration ?? throw new ArgumentNullException(nameof(configuration));
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            // Form properties
            this.Size = new Size(900, 600);
            this.Text = "Login Preview";
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.StartPosition = FormStartPosition.CenterParent;
            this.BackColor = config.GetBackgroundColor();

            CreateLayout();
        }

        private void CreateLayout()
        {
            // Left panel for branding
            leftPanel = new Panel
            {
                Size = new Size(450, 600),
                Location = new Point(0, 0),
                BackColor = config.GetPrimaryColor()
            };

            // Add background image if configured
            if (config.UseBackgroundImage && !string.IsNullOrEmpty(config.BackgroundImagePath) && File.Exists(config.BackgroundImagePath))
            {
                try
                {
                    leftPanel.BackgroundImage = Image.FromFile(config.BackgroundImagePath);
                    leftPanel.BackgroundImageLayout = ImageLayout.Stretch;
                }
                catch
                {
                    // If image loading fails, keep the solid color background
                }
            }

            this.Controls.Add(leftPanel);

            // Right panel for login form
            rightPanel = new Panel
            {
                Size = new Size(450, 600),
                Location = new Point(450, 0),
                BackColor = Color.White
            };
            this.Controls.Add(rightPanel);

            CreateLeftPanelContent();
            CreateRightPanelContent();
        }

        private void CreateLeftPanelContent()
        {
            // Left panel now only contains the configurable background color or image
            // All text elements (logo, station name, subtitle) are moved to the right panel
        }

        private void CreateRightPanelContent()
        {
            int centerX = rightPanel.Width / 2;
            int startY = 30;

            // Logo/Icon at the top
            logoBox = new PictureBox
            {
                Size = new Size(80, 80),
                Location = new Point(centerX - 40, startY),
                SizeMode = PictureBoxSizeMode.StretchImage,
                Image = GetLogoImage(),
                BackColor = Color.Transparent
            };
            rightPanel.Controls.Add(logoBox);

            // Welcome label - use fixed width for proper centering
            welcomeLabel = new Label
            {
                Text = config.WelcomeMessage,
                Font = new Font("Segoe UI", 18, FontStyle.Regular),
                ForeColor = config.GetStationTextColor(),
                Size = new Size(rightPanel.Width - 40, 30), // Fixed width with margins
                Location = new Point(20, startY + 90),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };
            rightPanel.Controls.Add(welcomeLabel);

            // Station name - use fixed width for proper centering
            stationLabel = new Label
            {
                Text = config.StationName,
                Font = new Font("Segoe UI", 28, FontStyle.Bold),
                ForeColor = config.GetStationTextColor(),
                Size = new Size(rightPanel.Width - 40, 40), // Fixed width with margins
                Location = new Point(20, startY + 120),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };
            rightPanel.Controls.Add(stationLabel);

            // Subtitle - use fixed width for proper centering and text wrapping
            subtitleLabel = new Label
            {
                Text = config.SubtitleMessage,
                Font = new Font("Segoe UI", 12, FontStyle.Regular),
                ForeColor = config.GetStationTextColor(),
                Size = new Size(rightPanel.Width - 40, 40), // Fixed width with margins, increased height for wrapping
                Location = new Point(20, startY + 160),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };
            rightPanel.Controls.Add(subtitleLabel);

            // Login title
            titleLabel = new Label
            {
                Text = "Sign In",
                Font = new Font("Segoe UI", 24, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41),
                AutoSize = true
            };
            titleLabel.Location = new Point(centerX - titleLabel.Width / 2, startY + 210);
            rightPanel.Controls.Add(titleLabel);

            // Username section - improved positioning
            var lblUsername = new Label
            {
                Text = "Username",
                Font = new Font("Segoe UI", 12, FontStyle.Regular),
                ForeColor = Color.FromArgb(73, 80, 87),
                Location = new Point(65, 280), // Fixed position instead of calculated
                AutoSize = true
            };
            rightPanel.Controls.Add(lblUsername);

            usernamePanel = new Panel
            {
                Size = new Size(320, 50),
                Location = new Point(65, 310), // Fixed position instead of calculated
                BackColor = Color.White,
                BorderStyle = BorderStyle.None
            };
            usernamePanel.Paint += (s, e) => DrawTextBoxBorder(e, usernamePanel, false);
            rightPanel.Controls.Add(usernamePanel);

            txtUsername = new TextBox
            {
                Size = new Size(300, 30),
                Location = new Point(10, 10),
                Font = new Font("Segoe UI", 12),
                BorderStyle = BorderStyle.None,
                BackColor = Color.White,
                Text = "demo_user"
            };
            // Fix cursor positioning by setting selection start to beginning
            txtUsername.Enter += (s, e) => { txtUsername.SelectionStart = 0; txtUsername.SelectionLength = 0; };
            usernamePanel.Controls.Add(txtUsername);

            // Password section - improved positioning
            var lblPassword = new Label
            {
                Text = "Password",
                Font = new Font("Segoe UI", 12, FontStyle.Regular),
                ForeColor = Color.FromArgb(73, 80, 87),
                Location = new Point(65, 380), // Fixed position instead of calculated
                AutoSize = true
            };
            rightPanel.Controls.Add(lblPassword);

            passwordPanel = new Panel
            {
                Size = new Size(320, 50),
                Location = new Point(65, 410), // Fixed position instead of calculated
                BackColor = Color.White,
                BorderStyle = BorderStyle.None
            };
            passwordPanel.Paint += (s, e) => DrawTextBoxBorder(e, passwordPanel, false);
            rightPanel.Controls.Add(passwordPanel);

            txtPassword = new TextBox
            {
                Size = new Size(300, 30),
                Location = new Point(10, 10),
                Font = new Font("Segoe UI", 12),
                BorderStyle = BorderStyle.None,
                BackColor = Color.White,
                PasswordChar = '●',
                Text = "password"
            };
            // Fix cursor positioning by setting selection start to beginning
            txtPassword.Enter += (s, e) => { txtPassword.SelectionStart = 0; txtPassword.SelectionLength = 0; };
            passwordPanel.Controls.Add(txtPassword);

            // Login button - improved positioning
            btnLogin = new Button
            {
                Text = "Sign In",
                Size = new Size(320, 45),
                Location = new Point(65, 480), // Fixed position instead of calculated
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat,
                BackColor = config.GetPrimaryColor(),
                ForeColor = Color.White,
                Cursor = Cursors.Hand
            };
            btnLogin.FlatAppearance.BorderSize = 0;
            rightPanel.Controls.Add(btnLogin);

            // Close button
            var closeButton = new Button
            {
                Text = "×",
                Size = new Size(30, 30),
                Location = new Point(410, 10),
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat,
                BackColor = Color.Transparent,
                ForeColor = Color.FromArgb(108, 117, 125),
                Cursor = Cursors.Hand
            };
            closeButton.FlatAppearance.BorderSize = 0;
            closeButton.Click += (s, e) => this.Close();
            rightPanel.Controls.Add(closeButton);
        }

        private Image GetLogoImage()
        {
            // Try to load custom logo if configured
            if (config.UseCustomLogo && !string.IsNullOrEmpty(config.LogoPath) && File.Exists(config.LogoPath))
            {
                try
                {
                    return Image.FromFile(config.LogoPath);
                }
                catch
                {
                    // If custom logo fails to load, fall back to default
                }
            }

            // Create default train/railway icon
            return CreateDefaultLogoImage();
        }

        private Image CreateDefaultLogoImage()
        {
            // Create a simple train/railway icon
            Bitmap logo = new Bitmap(80, 80);
            using (Graphics g = Graphics.FromImage(logo))
            {
                g.SmoothingMode = SmoothingMode.AntiAlias;
                g.Clear(Color.Transparent);

                // Draw a simple train icon
                using (Brush brush = new SolidBrush(Color.White))
                {
                    // Train body
                    g.FillRectangle(brush, 10, 30, 60, 25);
                    // Train front
                    g.FillRectangle(brush, 5, 35, 10, 15);
                    // Wheels
                    g.FillEllipse(brush, 15, 50, 8, 8);
                    g.FillEllipse(brush, 30, 50, 8, 8);
                    g.FillEllipse(brush, 45, 50, 8, 8);
                    g.FillEllipse(brush, 60, 50, 8, 8);
                }
            }
            return logo;
        }

        private void DrawTextBoxBorder(PaintEventArgs e, Panel panel, bool focused)
        {
            Color borderColor = focused ? config.GetPrimaryColor() : Color.FromArgb(206, 212, 218);
            int borderWidth = focused ? 2 : 1;

            using (Pen pen = new Pen(borderColor, borderWidth))
            {
                Rectangle rect = new Rectangle(0, 0, panel.Width - 1, panel.Height - 1);
                e.Graphics.DrawRectangle(pen, rect);
            }
        }
    }
}
